<?php
require '_header.php';
?>

<link rel="stylesheet" href="assets/transaction-styles.css">


<div class="p-4 sm:ml-64">
    <div class="mt-16 fade-in"> <!-- Adjusted for top navbar -->
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-gray-600 to-gray-700 rounded-xl shadow-lg p-6 mb-8">
            <!-- Breadcrumb -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2">
                    <li class="inline-flex items-center">
                        <a href="home.php"
                            class="inline-flex items-center text-sm font-medium text-gray-100 hover:text-white transition-colors">
                            <svg class="w-4 h-4 me-2" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-gray-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-gray-100">Transactions</span>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-gray-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-white">Draft</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Title Section -->
            <div class="flex items-center">
                <div class="bg-white bg-opacity-20 p-3 rounded-xl mr-4">
                    <i class="fas fa-file-alt text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-white mb-1">Draft & Voucher Bookings</h1>
                    <p class="text-gray-100">Manage draft bookings and voucher entries</p>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 mb-6">
            <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
                <div class="relative flex-1 max-w-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="searchInput" placeholder="Search bookings..."
                        class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500 transition-colors">
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-600">
                    <i class="fas fa-info-circle"></i>
                    <span id="bookingCount">Loading bookings...</span>
                </div>
            </div>
        </div>

        <!-- Bookings Grid -->
        <div id="bookingsContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php
            try {
                // SQL query to fetch booking details for both draft and voucher statuses
                $sql = "SELECT bb.booking_id AS booking_id, bb.referenceNum, bb.booking_status, oi.designation, bob.boatName
                                FROM cb_bookings bb
                                JOIN operator_info oi ON bb.resort_operator_id = oi.user_id
                                JOIN boat_operator_boatlist bob ON bb.boat_id = bob.id
                                WHERE bb.booking_status IN ('draft', 'voucher')";

                // Prepare and execute the statement
                $stmt = $pdo->prepare($sql);
                $stmt->execute();

                // Fetch the results
                $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

                if ($rows) {
                    foreach ($rows as $row) {
                        ?>
                        <div class="booking-card rounded-lg p-6 fade-in"
                            data-reference="<?= htmlspecialchars($row['referenceNum'] ?? ''); ?>"
                            data-resort="<?= htmlspecialchars($row['designation'] ?? ''); ?>"
                            data-boat="<?= htmlspecialchars($row['boatName'] ?? ''); ?>"
                            data-status="<?= htmlspecialchars($row['booking_status'] ?? ''); ?>">
                            <!-- Card Header -->
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                        <?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?>
                                    </h3>
                                    <div
                                        class="status-badge <?= $row['booking_status'] === 'voucher' ? 'status-voucher' : 'status-draft'; ?>">
                                        <?php if ($row['booking_status'] === 'voucher'): ?>
                                            <i class="fas fa-ticket-alt mr-1"></i>
                                            Voucher
                                        <?php else: ?>
                                            <i class="fas fa-file-alt mr-1"></i>
                                            Draft
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Booking Details -->
                            <div class="space-y-3 mb-4">
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-building text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Resort:</span>
                                    <span
                                        class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['designation']); ?></span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-ship text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Boat:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['boatName']); ?></span>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex flex-col sm:flex-row gap-2 pt-4 border-t border-gray-100">
                                <a href="add-passenger-info.php?id=<?= $row['booking_id']; ?>"
                                    class="action-button btn-edit flex-1">
                                    <i class="fas fa-edit mr-2"></i>
                                    Edit Booking
                                </a>
                                <button type="button" data-modal-target="deleteDraft-modal-<?= $row['booking_id']; ?>"
                                    data-modal-toggle="deleteDraft-modal-<?= $row['booking_id']; ?>"
                                    class="action-button btn-delete flex-1">
                                    <i class="fas fa-trash mr-2"></i>
                                    Delete
                                </button>
                            </div>
                        </div>

                        <!-- Delete Modal -->
                        <div id="deleteDraft-modal-<?= $row['booking_id']; ?>" tabindex="-1"
                            class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                            <div class="relative p-4 w-full max-w-md max-h-full">
                                <div class="relative bg-white rounded-lg shadow">
                                    <button type="button"
                                        class="absolute top-3 end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center"
                                        data-modal-hide="deleteDraft-modal-<?= $row['booking_id']; ?>">
                                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                                            viewBox="0 0 14 14">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                        </svg>
                                        <span class="sr-only">Close modal</span>
                                    </button>

                                    <div class="p-4 md:p-5 text-center">
                                        <form action="inc/inc.booking.php" method="POST">
                                            <svg class="mx-auto mb-4 text-gray-400 w-12 h-12" aria-hidden="true"
                                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                    stroke-width="2" d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                            </svg>
                                            <input type="hidden" name="csrf_token"
                                                value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                                            <input type="hidden" value="<?= $row['booking_id']; ?>" name="booking_id">
                                            <h3 class="mb-5 text-lg font-normal text-gray-700">
                                                Are you sure you want to <span class="text-red-600 font-bold">delete</span> booking?
                                            </h3>
                                            <button type="submit" name="deleteDraft"
                                                class="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center">
                                                Yes, I'm sure
                                            </button>
                                        </form>
                                        <button data-modal-hide="deleteDraft-modal-<?= $row['booking_id']; ?>" type="button"
                                            class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-300 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200">
                                            No, cancel
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php
                    }
                } else {
                    ?>
                    <div class="col-span-full">
                        <div class="booking-card rounded-lg p-8 text-center">
                            <div class="flex flex-col items-center justify-center">
                                <div class="bg-gray-100 p-4 rounded-full mb-4">
                                    <i class="fas fa-file-alt text-gray-500 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">No Draft or Voucher Bookings</h3>
                                <p class="text-gray-500 text-sm mb-4 max-w-sm">Draft and voucher bookings will appear here when
                                    they are created.</p>
                                <a href="home.php" class="action-button btn-edit">
                                    <i class="fas fa-arrow-left mr-2"></i>
                                    Back to Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            } catch (PDOException $e) {
                ?>
                <div class="col-span-full">
                    <div class="booking-card rounded-lg p-8 text-center border-red-200 bg-red-50">
                        <div class="flex flex-col items-center justify-center">
                            <div class="bg-red-100 p-4 rounded-full mb-4">
                                <i class="fas fa-exclamation-triangle text-red-500 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-red-900 mb-2">Error Loading Bookings</h3>
                            <p class="text-red-600 text-sm mb-4"><?= htmlspecialchars($e->getMessage()); ?></p>
                            <button onclick="location.reload()" class="action-button btn-edit">
                                <i class="fas fa-refresh mr-2"></i>
                                Try Again
                            </button>
                        </div>
                    </div>
                </div>
                <?php
            }
            ?>
        </div>
    </div>
    <?php
    require '_footer.php';
    ?>