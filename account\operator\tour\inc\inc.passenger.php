<?php
session_start([
    'cookie_secure'   => true,       // Only send cookie over HTTPS
    'cookie_httponly' => true,       // <PERSON><PERSON> cannot access the cookie
    'cookie_samesite' => 'Strict'    // Prevent CSRF in modern browsers
]);
include '../../../../connection/dbconnect.php';
include 'inc.function.php';
if ($_SESSION['loginStatus'] != "isLogin") {
    header("Location: ../logout.php");
    exit;
} else {

    if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST['registerPassenger'])) {
        try {
            if (
                !isset($_POST['csrf_token'], $_SESSION['csrf_token']) ||
                $_POST['csrf_token'] !== $_SESSION['csrf_token']
            ) {
                throw new Exception("Invalid CSRF token.");
            }
            // Optionally unset or regenerate the CSRF token after usage
            unset($_SESSION['csrf_token']);

            if (!isset($_SESSION['id'])) {
                throw new Exception("You must be logged in to perform this action.");
            }

            $bookingId = filter_input(INPUT_POST, 'bookingId', FILTER_VALIDATE_INT);
            if (!$bookingId) {
                throw new Exception("Invalid booking ID.");
            }

            // passengerType: strip tags, convert special chars, trim
            $passengerType = htmlspecialchars(trim($_POST['passengerType']), ENT_QUOTES, 'UTF-8');
            if (empty($passengerType)) {
                throw new Exception("Passenger type is required.");
            }

            // fullName: uppercase, sanitize
            $fullName = isset($_POST['fullName'])
                ? strtoupper(htmlspecialchars(trim($_POST['fullName']), ENT_QUOTES, 'UTF-8'))
                : null;
            if (empty($fullName)) {
                throw new Exception("Full name is required.");
            }

            // region-country
            $regCount = htmlspecialchars(trim($_POST['region-country'] ?? ''), ENT_QUOTES, 'UTF-8');
            if (empty($regCount)) {
                throw new Exception("Region/Country is required.");
            }

            // address
            $address = isset($_POST['address'])
                ? strtoupper(htmlspecialchars(trim($_POST['address']), ENT_QUOTES, 'UTF-8'))
                : null;
            if (empty($address)) {
                throw new Exception("Address is required.");
            }

            // gender
            $gender = htmlspecialchars(trim($_POST['gender'] ?? ''), ENT_QUOTES, 'UTF-8');
            if (empty($gender)) {
                throw new Exception("Gender is required.");
            }

            // age: must be a valid integer; returns int or false/null
            $age = filter_var($_POST['age'] ?? '', FILTER_VALIDATE_INT);
            if ($age === false || $age < 0) {
                throw new Exception("Invalid age provided.");
            }

            // contactNumber
            $contactNumber = htmlspecialchars(trim($_POST['contactNumber'] ?? ''), ENT_QUOTES, 'UTF-8');
            if (empty($contactNumber)) {
                throw new Exception("Contact number is required.");
            }

            $bookingDetails = getBookingDetails($pdo, $bookingId);
            if (!$bookingDetails) {
                throw new Exception("Booking not found or invalid.");
            }

            $counts = getTouristAndCrewCounts($pdo, $bookingId);

            $adultCount       = (int)$bookingDetails['total_adults'];
            $childrenCount    = (int)$bookingDetails['total_children'];
            $crewCount        = 4; // Hard-coded to 4 in your example
            $actual_adultCount    = (int)$counts['adults'];
            $actual_childrenCount = (int)$counts['children'];
            $actual_crewCount     = (int)$counts['crewTts'];

            if ($passengerType === 'tourist' && $age >= 9 && $actual_adultCount >= $adultCount) {
                throw new Exception("The number of adults exceeds the allowed total.");
            } elseif ($passengerType === 'tourist' && $age <= 8 && $actual_childrenCount >= $childrenCount) {
                throw new Exception("The number of children exceeds the allowed total.");
            } elseif ($passengerType === 'crewTts' && $actual_crewCount >= $crewCount) {
                throw new Exception("The number of crew exceeds the permitted limit.");
            }

            $pdo->beginTransaction();

            // Check if the tourist already exists in cb_tourists
            // Use case-insensitive check by forcing lower(full_name) = lower(:full_name)
            $stmt = $pdo->prepare("
            SELECT 1
            FROM cb_tourists
            WHERE booking_id = :booking_id
              AND LOWER(full_name) = LOWER(:full_name)
            LIMIT 1
        ");
            $stmt->execute([
                ':booking_id' => $bookingId,
                ':full_name'  => $fullName
            ]);

            if ($stmt->fetch()) {
                throw new Exception("Tourist already exists.");
            }

            // Insert new tourist record
            $stmt = $pdo->prepare("
            INSERT INTO cb_tourists
                (booking_id, full_name, demographic, address, gender, age, contact_number, info_type)
            VALUES
                (:bookingId, :fullName, :regCount, :address, :gender, :age, :contactNumber, :passengerType)
        ");
            $stmt->execute([
                ':bookingId'     => $bookingId,
                ':fullName'      => $fullName,
                ':regCount'      => $regCount,
                ':address'       => $address,
                ':gender'        => $gender,
                ':age'           => $age,
                ':contactNumber' => $contactNumber,
                ':passengerType' => $passengerType
            ]);

            // Check if this is a voucher booking and handle voucher logic
            if ($bookingDetails['payment_status'] === 'voucher' && $passengerType === 'tourist') {
                // Get port information to determine voucher type
                $portName = $bookingDetails['portName'];
                $tourOperatorId = $bookingDetails['tour_operator_id'];

                // Determine voucher type based on port name (consistent with delete logic)
                $isVinzonsPort = (stripos($portName, 'vinzons') !== false);
                $voucherType = $isVinzonsPort ? 'voucher_vinzons' : 'voucher_others';
                
                // Update voucher_use in cb_payments (+1)
                $stmt = $pdo->prepare("
                    UPDATE cb_payments 
                    SET voucher_use = voucher_use + 1 
                    WHERE booking_id = :booking_id
                ");
                $stmt->execute([':booking_id' => $bookingId]);
                
                // Update voucher count in cb_vouchers (-1)
                $stmt = $pdo->prepare("
                    UPDATE cb_vouchers 
                    SET $voucherType = $voucherType - 1 
                    WHERE operator_id = :operator_id AND $voucherType > 0
                ");
                $stmt->execute([':operator_id' => $tourOperatorId]);
                
                // Check if the voucher update was successful
                if ($stmt->rowCount() === 0) {
                    throw new Exception("No vouchers available for this port or operator.");
                }

                error_log("Voucher used for new tourist. Booking ID: $bookingId, Voucher type: $voucherType, Operator ID: $tourOperatorId");
            }

            // Commit transaction
            $pdo->commit();

            // --------------------------------------------------------------------
            // 8. Success message and redirect
            // --------------------------------------------------------------------
            $_SESSION['success'] = "Passenger has been added successfully.";
            header("Location: ../add-passenger-info.php?id=" . urlencode($bookingId));
            exit();
        } catch (Exception $e) {
            // Rollback if in a transaction
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }

            // Optional: Log errors for debugging (do not show details to user)
            // error_log("Error adding passenger: " . $e->getMessage());

            $_SESSION['error'] = $e->getMessage();
            header("Location: ../add-passenger-info.php?id=" . urlencode($bookingId));
            exit();
        }
    }

    if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST['deletePassenger'])) {
        try {
            if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
                throw new Exception("Invalid CSRF token.");
            }

            $passengerId = $_POST['passengerId'];
            $bookingId = $_POST['bookingId'];

            $pdo->beginTransaction();

            // Get passenger details before deletion to check if it's a tourist and get booking info
            $stmt = $pdo->prepare("
                SELECT ct.info_type, ct.booking_id, cb.payment_status, cb.tour_operator_id, pl.portName
                FROM cb_tourists ct
                JOIN cb_bookings cb ON ct.booking_id = cb.booking_id
                LEFT JOIN cb_payments cp ON cb.booking_id = cp.booking_id
                LEFT JOIN port_list pl ON cb.port_id = pl.id
                WHERE ct.tourist_id = :tourist_id
            ");
            $stmt->execute([':tourist_id' => $passengerId]);
            $passengerInfo = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$passengerInfo) {
                throw new Exception('Passenger not found.');
            }

            // Check if this is a voucher booking and the passenger is a tourist
            $isVoucherBooking = ($passengerInfo['payment_status'] === 'voucher');
            $isTourist = ($passengerInfo['info_type'] === 'tourist');

            // Delete the passenger
            $stmt = $pdo->prepare("DELETE FROM cb_tourists WHERE tourist_id = :tourist_id");
            $stmt->execute([':tourist_id' => $passengerId]);

            if ($stmt->rowCount() === 0) {
                throw new Exception('Failed to delete passenger.');
            }

            // Handle voucher restoration for voucher bookings with tourist passengers
            if ($isVoucherBooking && $isTourist) {
                $tourOperatorId = $passengerInfo['tour_operator_id'];
                $portName = $passengerInfo['portName'];

                // Determine voucher type based on port name
                $isVinzonsPort = (stripos($portName, 'vinzons') !== false);
                $voucherType = $isVinzonsPort ? 'voucher_vinzons' : 'voucher_others';

                // Decrease voucher_use in cb_payments (-1)
                $stmt = $pdo->prepare("
                    UPDATE cb_payments
                    SET voucher_use = GREATEST(voucher_use - 1, 0)
                    WHERE booking_id = :booking_id
                ");
                $stmt->execute([':booking_id' => $bookingId]);

                // Restore voucher count in cb_vouchers (+1)
                $stmt = $pdo->prepare("
                    UPDATE cb_vouchers
                    SET $voucherType = $voucherType + 1
                    WHERE operator_id = :operator_id
                ");
                $stmt->execute([':operator_id' => $tourOperatorId]);

                // If no voucher record exists for this operator, create one
                if ($stmt->rowCount() === 0) {
                    $stmt = $pdo->prepare("
                        INSERT INTO cb_vouchers (operator_id, $voucherType)
                        VALUES (:operator_id, 1)
                        ON DUPLICATE KEY UPDATE $voucherType = $voucherType + 1
                    ");
                    $stmt->execute([':operator_id' => $tourOperatorId]);
                }

                error_log("Voucher restored for deleted tourist. Booking ID: $bookingId, Voucher type: $voucherType, Operator ID: $tourOperatorId");
            }

            $pdo->commit();

            $_SESSION['success'] = "Passenger info has been deleted successfully.";
            header("Location: ../add-passenger-info.php?id=" . $bookingId);
            exit();
        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../add-passenger-info.php?id=" . $bookingId);
            exit();
        }
    }
}
