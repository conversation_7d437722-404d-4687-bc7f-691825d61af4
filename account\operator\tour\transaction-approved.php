<?php
require '_header.php';
?>

<link rel="stylesheet" href="assets/transaction-styles.css">

<div class="p-4 sm:ml-64">
    <div class="mt-16 fade-in"> <!-- Adjusted for top navbar -->
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-green-600 to-green-700 rounded-xl shadow-lg p-6 mb-8">
            <!-- Breadcrumb -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2">
                    <li class="inline-flex items-center">
                        <a href="home.php"
                            class="inline-flex items-center text-sm font-medium text-green-100 hover:text-white transition-colors">
                            <svg class="w-4 h-4 me-2" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-green-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-green-100">Transactions</span>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-green-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-white">Approved</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Title Section -->
            <div class="flex items-center">
                <div class="bg-white bg-opacity-20 p-3 rounded-xl mr-4">
                    <i class="fas fa-check-circle text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-white mb-1">Approved Bookings</h1>
                    <p class="text-green-100">Files Ready to downloads.</p>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 mb-6">
            <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
                <div class="relative flex-1 max-w-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="searchInput" placeholder="Search bookings..."
                        class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-600">
                    <i class="fas fa-info-circle"></i>
                    <span id="bookingCount">Loading bookings...</span>
                </div>
            </div>
        </div>

        <!-- Bookings Grid -->
        <div id="bookingsContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php
            try {
                $booking_status = "approved";

                $bookingDetails = getAllBookingDetails($pdo, $booking_status);

                if ($bookingDetails) {
                    foreach ($bookingDetails as $row) {
                        ?>
                        <!-- Booking Card -->
                        <div class="booking-card rounded-lg p-6 fade-in"
                            data-reference="<?= htmlspecialchars($row['referenceNum'] ?? ''); ?>"
                            data-resort="<?= htmlspecialchars($row['designation'] ?? ''); ?>"
                            data-boat="<?= htmlspecialchars($row['boatName'] ?? ''); ?>" data-status="approved">
                            <!-- Card Header -->
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                        <?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?>
                                    </h3>
                                    <div class="status-badge status-approved">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        Approved
                                    </div>
                                </div>
                            </div>

                            <!-- Booking Details -->
                            <div class="space-y-3 mb-4">
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-building text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Resort:</span>
                                    <span
                                        class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['designation']); ?></span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-ship text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Boat:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['boatName']); ?></span>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="grid grid-cols-2 gap-2 pt-4 border-t border-gray-100">
                                <button data-modal-target="view-approvals-modal-<?= $row['booking_id']; ?>"
                                    data-modal-toggle="view-approvals-modal-<?= $row['booking_id']; ?>" type="button"
                                    class="action-button btn-download col-span-2">
                                    <i class="fas fa-download mr-2"></i>
                                    Download
                                </button>
                            </div>
                        </div>
                        <!-- View Passenger Modal -->
                        <div id="view-approvals-modal-<?= $row['booking_id']; ?>" tabindex="-1"
                            class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                            <div class="relative w-full max-w-lg max-h-full"> <!-- Increased max-width to max-w-lg -->
                                <!-- Modal Container -->
                                <div class="relative bg-white rounded-lg shadow-lg border border-gray-200">
                                    <!-- Modal Header -->
                                    <div
                                        class="flex items-center justify-between p-4 bg-gradient-to-r from-green-600 to-green-700 rounded-t-lg">
                                        <div class="flex items-center">
                                            <i class="fas fa-file-download text-white mr-2"></i>
                                            <h3 class="text-xl font-semibold text-white">
                                                Download Documents
                                            </h3>
                                        </div>
                                        <button type="button"
                                            class="text-white hover:text-gray-200 rounded-full w-8 h-8 flex justify-center items-center transition-colors duration-200 bg-green-800 hover:bg-green-900"
                                            data-modal-hide="view-approvals-modal-<?= $row['booking_id']; ?>">
                                            <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                                                viewBox="0 0 14 14">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                    stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                            </svg>
                                            <span class="sr-only">Close modal</span>
                                        </button>
                                    </div>

                                    <!-- Modal Body -->
                                    <div class="p-5 bg-white">
                                        <!-- Booking Information -->
                                        <div class="mb-4 bg-gray-50 p-4 rounded-lg border border-gray-200">
                                            <div class="flex items-center mb-2">
                                                <i class="fas fa-info-circle text-green-600 mr-2"></i>
                                                <h4 class="text-sm font-semibold text-gray-800">Booking Information</h4>
                                            </div>
                                            <div class="grid grid-cols-2 gap-3">
                                                <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                    <p class="text-xs font-medium text-gray-700">Reference #:</p>
                                                    <p class="text-sm font-semibold text-gray-900">
                                                        <?= htmlspecialchars($row['referenceNum']); ?>
                                                    </p>
                                                </div>
                                                <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                    <p class="text-xs font-medium text-gray-700">Resort:</p>
                                                    <p class="text-sm font-semibold text-gray-900">
                                                        <?= htmlspecialchars($row['designation']); ?>
                                                    </p>
                                                </div>
                                                <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                    <p class="text-xs font-medium text-gray-700">Boat:</p>
                                                    <p class="text-sm font-semibold text-gray-900">
                                                        <?= htmlspecialchars($row['boatName']); ?>
                                                    </p>
                                                </div>
                                                <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                    <p class="text-xs font-medium text-gray-700">Status:</p>
                                                    <p class="text-sm font-semibold text-green-600">Approved</p>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Available Documents -->
                                        <div class="mb-2">
                                            <div class="flex items-center mb-3">
                                                <i class="fas fa-file-alt text-green-600 mr-2"></i>
                                                <h4 class="text-sm font-semibold text-gray-800">Available Documents</h4>
                                            </div>
                                            <div class="space-y-3">
                                                <div
                                                    class="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                                                    <div class="flex items-center justify-between p-4">
                                                        <div class="flex items-center">
                                                            <div class="bg-green-100 p-2 rounded-lg mr-3">
                                                                <i class="fas fa-id-card text-green-600"></i>
                                                            </div>
                                                            <div>
                                                                <h5 class="text-sm font-medium text-gray-900">Tourism Pass</h5>
                                                                <p class="text-xs text-gray-500">Official tourism pass document</p>
                                                            </div>
                                                        </div>
                                                        <a href="download/tourism-pass.php?id=<?= $row['booking_id']; ?>"
                                                            class="inline-flex items-center justify-center bg-green-600 hover:bg-green-700 text-white font-medium rounded-md text-xs px-3 py-2 transition-colors duration-200">
                                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                                                stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1">
                                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                                    d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3" />
                                                            </svg>
                                                            Download
                                                        </a>
                                                    </div>
                                                </div>

                                                <div
                                                    class="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                                                    <div class="flex items-center justify-between p-4">
                                                        <div class="flex items-center">
                                                            <div class="bg-green-100 p-2 rounded-lg mr-3">
                                                                <i class="fas fa-list-alt text-green-600"></i>
                                                            </div>
                                                            <div>
                                                                <h5 class="text-sm font-medium text-gray-900">Manifesto</h5>
                                                                <p class="text-xs text-gray-500">Passenger and crew manifest</p>
                                                            </div>
                                                        </div>
                                                        <a href="download/manifesto.php?id=<?= $row['booking_id']; ?>"
                                                            class="inline-flex items-center justify-center bg-green-600 hover:bg-green-700 text-white font-medium rounded-md text-xs px-3 py-2 transition-colors duration-200">
                                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                                                stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1">
                                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                                    d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3" />
                                                            </svg>
                                                            Download
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Modal Footer -->
                                    <div
                                        class="flex justify-between items-center p-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
                                        <a href="#" onclick="downloadAllDocuments(<?= $row['booking_id']; ?>); return false;"
                                            class="inline-flex items-center justify-center bg-green-600 hover:bg-green-700 text-white font-medium rounded-md text-xs px-4 py-2 transition-colors duration-200">
                                            <i class="fas fa-download mr-1"></i> Download All
                                        </a>
                                        <button data-modal-hide="view-approvals-modal-<?= $row['booking_id']; ?>" type="button"
                                            class="py-2 px-4 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-all duration-200">
                                            Close
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php
                    }
                } else {
                    // No approved bookings found
                    ?>
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
                        <div class="max-w-md mx-auto">
                            <div class="bg-green-100 p-4 rounded-full inline-block mb-4">
                                <i class="fas fa-check-circle text-green-400 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Approved Bookings</h3>
                            <p class="text-gray-500 mb-4">There are currently no approved bookings ready for download.</p>
                            <a href="home.php"
                                class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors">
                                <i class="fas fa-arrow-left mr-2"></i>
                                Back to Dashboard
                            </a>
                        </div>
                    </div>
                    <?php
                }
            } catch (PDOException $e) {
                ?>
                <div class="bg-white rounded-xl shadow-sm border border-red-200 p-8 text-center">
                    <div class="max-w-md mx-auto">
                        <div class="bg-red-100 p-4 rounded-full inline-block mb-4">
                            <i class="fas fa-exclamation-triangle text-red-400 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Error Loading Bookings</h3>
                        <p class="text-gray-500 mb-4">There was an error loading the approved bookings:
                            <?= htmlspecialchars($e->getMessage()); ?>
                        </p>
                        <button onclick="window.location.reload()"
                            class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors">
                            <i class="fas fa-refresh mr-2"></i>
                            Try Again
                        </button>
                    </div>
                </div>
                <?php
            }
            ?>
        </div>
    </div>
    <?php
    require '_footer.php';
    ?>