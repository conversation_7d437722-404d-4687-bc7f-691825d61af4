<?php
require '_header.php';
?>

<link rel="stylesheet" href="assets/transaction-styles.css">

<div class="p-4 sm:ml-64">
    <div class="mt-16 fade-in"> <!-- Adjusted for top navbar -->
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl shadow-lg p-6 mb-8">
            <!-- Breadcrumb -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2">
                    <li class="inline-flex items-center">
                        <a href="home.php"
                            class="inline-flex items-center text-sm font-medium text-blue-100 hover:text-white transition-colors">
                            <svg class="w-4 h-4 me-2" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-blue-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-blue-100">Transactions</span>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-blue-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-white">Pending</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Title Section -->
            <div class="flex items-center">
                <div class="bg-white bg-opacity-20 p-3 rounded-xl mr-4">
                    <i class="fas fa-clock text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-white mb-1">Pending Bookings</h1>
                    <p class="text-blue-100">Bookings awaiting approval from operators</p>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 mb-6">
            <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
                <div class="relative flex-1 max-w-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="searchInput" placeholder="Search bookings..."
                        class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-600">
                    <i class="fas fa-info-circle"></i>
                    <span id="bookingCount">Loading bookings...</span>
                </div>
            </div>
        </div>

        <!-- Bookings Grid -->
        <div id="bookingsContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php
            try {
                $booking_status = "pending";
                $bookingDetails = getAllBookingDetails($pdo, $booking_status);

                if ($bookingDetails && count($bookingDetails) > 0) {
                    foreach ($bookingDetails as $row) {
                        // Check if the booking is canceled
                        $isCanceled = isset($row['cancellation_booking_status']) && $row['cancellation_booking_status'] === 'request';

                        // Check if both resort and boat are approved
                        if (!$isCanceled && $row['mtho'] === "Waiting") {
                            $bothApproved = ($row['resort'] === "Approved" && $row['boat'] === "Approved");
                        } else {
                            $bothApproved = false;
                        }

                        if ($row['payment_status'] === 'voucher') {
                            $showBadge = '<span class="badge inline-flex items-center bg-purple-100 text-purple-800 border border-purple-300 rounded-full px-3 py-1 text-xs font-semibold">
                            <i class="fas fa-ticket-alt mr-1 text-purple-500"></i>
                            Voucher
                            </span>';
                        } else {
                            $showBadge = '';
                        }
                        ?>
                        <!-- Booking Card -->
                        <div class="booking-card rounded-lg p-6 fade-in"
                            data-reference="<?= htmlspecialchars($row['referenceNum'] ?? ''); ?>"
                            data-resort="<?= htmlspecialchars($row['designation'] ?? ''); ?>"
                            data-boat="<?= htmlspecialchars($row['boatName'] ?? ''); ?>"
                            data-status="<?= $isCanceled ? 'canceled' : ($bothApproved ? 'ready' : 'pending'); ?>">
                            <!-- Card Header -->
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                        <?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?>
                                        <?= $showBadge; ?>
                                    </h3>
                                    <div
                                        class="status-badge <?= $isCanceled ? 'status-canceled' : ($bothApproved ? 'status-ready' : 'status-pending'); ?>">
                                        <?php if ($isCanceled): ?>
                                            <i class="fas fa-ban mr-1"></i>
                                            Requesting Cancelation
                                        <?php elseif ($bothApproved): ?>
                                            <i class="fas fa-check-circle mr-1"></i>
                                            Ready for Payment
                                        <?php else: ?>
                                            <i class="fas fa-clock mr-1"></i>
                                            Awaiting Approval
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Booking Details -->
                            <div class="space-y-3 mb-4">
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-building text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Resort:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['designation']); ?>
                                    </span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-ship text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Boat:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['boatName']); ?></span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-calendar text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Date:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['check_in_date']); ?> -
                                        <?= htmlspecialchars($row['check_out_date']); ?>
                                    </span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-users text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Total Pax:</span>
                                    <span
                                        class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['total_adults'] + $row['total_children']); ?>
                                    </span>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="grid grid-cols-2 gap-2 pt-4 border-t border-gray-100">
                                <button data-modal-target="view-approvals-modal-<?= $row['booking_id']; ?>"
                                    data-modal-toggle="view-approvals-modal-<?= $row['booking_id']; ?>" type="button"
                                    class="action-button btn-view">
                                    <i class="fas fa-eye mr-2"></i>
                                    View Details
                                </button>

                                <?php if ($bothApproved && !$isCanceled): ?>
                                    <a href="payment-tdf.php?id=<?= $row['booking_id']; ?>" class="action-button btn-payment">
                                        <i class="fas fa-credit-card mr-2"></i>
                                        Payment
                                    </a>
                                <?php endif; ?>

                                <button data-modal-target="cancel-booking-modal-<?= $row['booking_id']; ?>"
                                    data-modal-toggle="cancel-booking-modal-<?= $row['booking_id']; ?>" type="button"
                                    class="action-button btn-cancel <?= $bothApproved && !$isCanceled ? 'col-span-2' : ''; ?>"
                                    <?= $isCanceled ? 'disabled' : ''; ?>>
                                    <i class="fas fa-times mr-2"></i>
                                    <?= $isCanceled ? 'Canceled' : 'Cancel'; ?>
                                </button>
                            </div>
                        </div>

                        <!-- View Passenger Modal -->
                        <div id="view-approvals-modal-<?= $row['booking_id']; ?>" tabindex="-1"
                            class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full backdrop-blur-sm bg-gray-900/50">
                            <div class="relative w-full max-w-md max-h-full">
                                <!-- Increased max-width to max-w-md for better readability -->
                                <!-- Modal Container -->
                                <div class="relative bg-white rounded-lg shadow-lg">
                                    <!-- Modal Header -->
                                    <div
                                        class="flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-blue-700 rounded-t-lg">
                                        <div class="flex items-center">
                                            <i class="fas fa-clipboard-check text-white mr-2"></i>
                                            <h3 class="text-xl font-semibold text-white">
                                                Approval Status
                                            </h3>
                                        </div>
                                        <button type="button"
                                            class="text-white hover:text-gray-200 rounded-full w-8 h-8 flex justify-center items-center transition-colors duration-200 bg-blue-800 hover:bg-blue-900"
                                            data-modal-hide="view-approvals-modal-<?= $row['booking_id']; ?>">
                                            <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                                                viewBox="0 0 14 14">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                    stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                            </svg>
                                            <span class="sr-only">Close modal</span>
                                        </button>
                                    </div>

                                    <!-- Modal Body -->
                                    <div class="p-5 bg-white rounded-lg space-y-5">
                                        <!-- Booking Reference -->
                                        <div class="bg-blue-50 p-3 rounded-lg border border-blue-100 mb-4">
                                            <p class="text-xs text-blue-500 uppercase font-medium">Reference Number</p>
                                            <p class="text-sm font-semibold text-blue-800 mt-1">
                                                <?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?>
                                            </p>
                                        </div>

                                        <!-- Status Cards -->
                                        <div class="grid grid-cols-1 gap-3">
                                            <!-- Resort -->
                                            <div
                                                class="bg-white border border-gray-200 rounded-lg p-3 shadow-sm flex items-center justify-between">
                                                <div class="flex items-center">
                                                    <div class="bg-blue-100 p-2 rounded-full mr-3">
                                                        <i class="fas fa-hotel text-blue-600"></i>
                                                    </div>
                                                    <span class="text-sm font-medium text-gray-700">Resort</span>
                                                </div>
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                                            <?php if ($row['resort'] === 'Approved'): ?>
                                                                bg-green-100 text-green-800
                                                            <?php elseif ($row['resort'] === 'Pending'): ?>
                                                                bg-yellow-100 text-yellow-800
                                                            <?php else: ?>
                                                                bg-gray-100 text-gray-800
                                                            <?php endif; ?>">
                                                    <?php if ($row['resort'] === 'Approved'): ?>
                                                        <i class="fas fa-check-circle mr-1"></i>
                                                    <?php elseif ($row['resort'] === 'Pending'): ?>
                                                        <i class="fas fa-clock mr-1"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-hourglass-half mr-1"></i>
                                                    <?php endif; ?>
                                                    <?= $row['resort']; ?>
                                                </span>
                                            </div>

                                            <!-- Boat -->
                                            <div
                                                class="bg-white border border-gray-200 rounded-lg p-3 shadow-sm flex items-center justify-between">
                                                <div class="flex items-center">
                                                    <div class="bg-blue-100 p-2 rounded-full mr-3">
                                                        <i class="fas fa-ship text-blue-600"></i>
                                                    </div>
                                                    <span class="text-sm font-medium text-gray-700">Boat</span>
                                                </div>
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                                            <?php if ($row['boat'] === 'Approved'): ?>
                                                                bg-green-100 text-green-800
                                                            <?php elseif ($row['boat'] === 'Pending'): ?>
                                                                bg-yellow-100 text-yellow-800
                                                            <?php else: ?>
                                                                bg-gray-100 text-gray-800
                                                            <?php endif; ?>">
                                                    <?php if ($row['boat'] === 'Approved'): ?>
                                                        <i class="fas fa-check-circle mr-1"></i>
                                                    <?php elseif ($row['boat'] === 'Pending'): ?>
                                                        <i class="fas fa-clock mr-1"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-hourglass-half mr-1"></i>
                                                    <?php endif; ?>
                                                    <?= $row['boat']; ?>
                                                </span>
                                            </div>

                                            <!-- Treasurer -->
                                            <div
                                                class="bg-white border border-gray-200 rounded-lg p-3 shadow-sm flex items-center justify-between">
                                                <div class="flex items-center">
                                                    <div class="bg-blue-100 p-2 rounded-full mr-3">
                                                        <i class="fas fa-money-bill-wave text-blue-600"></i>
                                                    </div>
                                                    <span class="text-sm font-medium text-gray-700">Treasurer</span>
                                                </div>
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                                            <?php if ($row['treasurer'] === 'Approved'): ?>
                                                                bg-green-100 text-green-800
                                                            <?php elseif ($row['treasurer'] === 'Pending'): ?>
                                                                bg-yellow-100 text-yellow-800
                                                            <?php else: ?>
                                                                bg-gray-100 text-gray-800
                                                            <?php endif; ?>">
                                                    <?php if ($row['treasurer'] === 'Approved'): ?>
                                                        <i class="fas fa-check-circle mr-1"></i>
                                                    <?php elseif ($row['treasurer'] === 'Pending'): ?>
                                                        <i class="fas fa-clock mr-1"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-hourglass-half mr-1"></i>
                                                    <?php endif; ?>
                                                    <?= $row['treasurer']; ?>
                                                </span>
                                            </div>

                                            <!-- MTHO -->
                                            <div
                                                class="bg-white border border-gray-200 rounded-lg p-3 shadow-sm flex items-center justify-between">
                                                <div class="flex items-center">
                                                    <div class="bg-blue-100 p-2 rounded-full mr-3">
                                                        <i class="fas fa-building text-blue-600"></i>
                                                    </div>
                                                    <span class="text-sm font-medium text-gray-700">MTHO</span>
                                                </div>
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                                            <?php if ($row['mtho'] === 'Approved'): ?>
                                                                bg-green-100 text-green-800
                                                            <?php elseif ($row['mtho'] === 'Pending'): ?>
                                                                bg-yellow-100 text-yellow-800
                                                            <?php else: ?>
                                                                bg-gray-100 text-gray-800
                                                            <?php endif; ?>">
                                                    <?php if ($row['mtho'] === 'Approved'): ?>
                                                        <i class="fas fa-check-circle mr-1"></i>
                                                    <?php elseif ($row['mtho'] === 'Pending'): ?>
                                                        <i class="fas fa-clock mr-1"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-hourglass-half mr-1"></i>
                                                    <?php endif; ?>
                                                    <?= $row['mtho']; ?>
                                                </span>
                                            </div>
                                        </div>

                                        <?php if ($row['resort'] === 'Approved' && $row['boat'] === 'Approved' && $row['mtho'] === 'Waiting'): ?>
                                            <!-- Approval Complete Message -->
                                            <div class="flex p-4 mt-2 text-green-800 rounded-lg bg-green-50 border border-green-200"
                                                role="alert">
                                                <div class="flex-shrink-0">
                                                    <i class="fas fa-check-circle text-green-600"></i>
                                                </div>
                                                <div class="ml-3 text-sm font-medium text-green-800">
                                                    Both Resort and Boat operators have approved this booking. You can now proceed to
                                                    payment.
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <!-- Waiting for Approval Message -->
                                            <div class="flex p-4 mt-2 text-yellow-800 rounded-lg bg-yellow-50 border border-yellow-200"
                                                role="alert">
                                                <div class="flex-shrink-0">
                                                    <i class="fas fa-exclamation-circle text-yellow-600"></i>
                                                </div>
                                                <div class="ml-3 text-sm font-medium text-yellow-800">
                                                    Waiting for approval from all required operators before proceeding to payment.
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Modal Footer -->
                                    <div class="flex justify-end items-center p-4 bg-gray-50 border-t border-gray-100 rounded-b-lg">
                                        <?php if ($row['resort'] === 'Approved' && $row['boat'] === 'Approved' && $row['mtho'] === 'Waiting'): ?>
                                            <a href="payment-tdf.php?id=<?= $row['booking_id']; ?>"
                                                class="py-2 px-4 mr-3 text-sm font-semibold text-white bg-green-600 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all duration-200">
                                                <i class="fas fa-credit-card mr-1"></i> Proceed to Payment
                                            </a>
                                        <?php endif; ?>
                                        <button data-modal-hide="view-approvals-modal-<?= $row['booking_id']; ?>" type="button"
                                            class="py-2 px-4 text-sm font-semibold text-white bg-red-500 rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-400 transition-all duration-200">
                                            <i class="fas fa-times mr-1"></i> Close
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Cancel Booking Modal -->
                        <div id="cancel-booking-modal-<?= $row['booking_id']; ?>" tabindex="-1"
                            class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full backdrop-blur-sm bg-gray-900/50">
                            <div class="relative w-full max-w-md max-h-full">
                                <!-- Modal Container -->
                                <div class="relative bg-white rounded-lg shadow-lg">
                                    <!-- Modal Header -->
                                    <div
                                        class="flex items-center justify-between p-4 bg-gradient-to-r from-red-600 to-red-700 rounded-t-lg">
                                        <div class="flex items-center">
                                            <i class="fas fa-exclamation-triangle text-white mr-2"></i>
                                            <h3 class="text-xl font-semibold text-white">
                                                Request Cancellation
                                            </h3>
                                        </div>
                                        <button type="button"
                                            class="text-white hover:text-gray-200 rounded-full w-8 h-8 flex justify-center items-center transition-colors duration-200 bg-red-800 hover:bg-red-900"
                                            data-modal-hide="cancel-booking-modal-<?= $row['booking_id']; ?>">
                                            <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                                                viewBox="0 0 14 14">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                    stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                            </svg>
                                            <span class="sr-only">Close modal</span>
                                        </button>
                                    </div>

                                    <!-- Modal Body -->
                                    <div class="p-5 bg-white rounded-lg space-y-4">
                                        <!-- Warning Message -->
                                        <div class="flex p-4 text-red-800 rounded-lg bg-red-50 border border-red-200" role="alert">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-exclamation-triangle text-red-600"></i>
                                            </div>
                                            <div class="ml-3 text-sm font-medium text-red-800">
                                                <strong>Warning:</strong> This will submit a cancellation request. Are you sure you
                                                want to request cancellation for this booking?
                                            </div>
                                        </div>

                                        <!-- Booking Reference -->
                                        <div class="bg-gray-50 p-3 rounded-lg border border-gray-100">
                                            <p class="text-xs text-gray-500 uppercase font-medium">Reference Number</p>
                                            <p class="text-sm font-semibold text-gray-800 mt-1">
                                                <?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?>
                                            </p>
                                        </div>

                                        <!-- Booking Details -->
                                        <div class="space-y-2">
                                            <div class="flex justify-between">
                                                <span class="text-sm text-gray-600">Resort:</span>
                                                <span
                                                    class="text-sm font-medium text-gray-900"><?= htmlspecialchars($row['designation']); ?></span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-sm text-gray-600">Boat:</span>
                                                <span
                                                    class="text-sm font-medium text-gray-900"><?= htmlspecialchars($row['boatName']); ?></span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Modal Footer -->
                                    <div
                                        class="flex justify-end items-center p-4 bg-gray-50 border-t border-gray-100 rounded-b-lg space-x-3">
                                        <button data-modal-hide="cancel-booking-modal-<?= $row['booking_id']; ?>" type="button"
                                            class="py-2 px-4 text-sm font-semibold text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-all duration-200">
                                            <i class="fas fa-times mr-1"></i> No, Keep Booking
                                        </button>
                                        <form action="inc/inc.booking.php" method="POST" class="inline">
                                            <input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token']; ?>">
                                            <input type="hidden" name="booking_id" value="<?= $row['booking_id']; ?>">
                                            <input type="hidden" name="referenceNumber"
                                                value="<?= htmlspecialchars($row['referenceNum']); ?>">
                                            <button type="submit" name="cancelBooking"
                                                class="py-2 px-4 text-sm font-semibold text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-400 transition-all duration-200">
                                                <i class="fas fa-paper-plane mr-1"></i> Yes, Submit Request
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <?php
                    }
                } else {
                    ?>
                    <div class="col-span-full">
                        <div class="booking-card rounded-lg p-8 text-center">
                            <div class="flex flex-col items-center justify-center">
                                <div class="bg-blue-100 p-4 rounded-full mb-4">
                                    <i class="fas fa-clock text-blue-500 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">No Pending Bookings</h3>
                                <p class="text-gray-500 text-sm mb-4 max-w-sm">Pending bookings will appear here when they are
                                    awaiting approval.</p>
                                <a href="home.php" class="action-button btn-view">
                                    <i class="fas fa-arrow-left mr-2"></i>
                                    Back to Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            } catch (PDOException $e) {
                ?>
                <div class="col-span-full">
                    <div class="booking-card rounded-lg p-8 text-center border-red-200 bg-red-50">
                        <div class="flex flex-col items-center justify-center">
                            <div class="bg-red-100 p-4 rounded-full mb-4">
                                <i class="fas fa-exclamation-triangle text-red-500 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-red-900 mb-2">Error Loading Bookings</h3>
                            <p class="text-red-600 text-sm mb-4"><?= htmlspecialchars($e->getMessage()); ?></p>
                            <button onclick="location.reload()" class="action-button btn-view">
                                <i class="fas fa-refresh mr-2"></i>
                                Try Again
                            </button>
                        </div>
                    </div>
                </div>
                <?php
            }
            ?>
        </div>
    </div>

    <?php
    require '_footer.php';
    ?>