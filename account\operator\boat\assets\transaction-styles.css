/* Shared Transaction Styles for Boat Operator */

/* Custom Card Styles */
.custom-card {
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.custom-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-color: #d1d5db;
}

/* Action Button Styles */
.btn-close,
.btn-submit {
    transition: all 0.2s ease;
    font-weight: 500;
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Pending Status Styles */
.btn-close.pending,
.btn-submit.pending {
    background-color: #2563eb;
    color: white;
}

.btn-close.pending:hover,
.btn-submit.pending:hover {
    background-color: #1d4ed8;
}

/* Approved Status Styles */
.btn-close.approved,
.btn-submit.approved {
    background-color: #16a34a;
    color: white;
}

.btn-close.approved:hover,
.btn-submit.approved:hover {
    background-color: #15803d;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Status Badge Styles */
.status-pending {
    background-color: #fef3c7;
    color: #d97706;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-ready {
    background-color: #dcfce7;
    color: #16a34a;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Enhanced hover effects for table rows */
.table-row-hover {
    transition: background-color 0.2s ease;
}

.table-row-hover:hover {
    background-color: #f9fafb;
}

/* Modal enhancements */
.modal-content {
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}